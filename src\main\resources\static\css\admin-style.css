/**
 * 管理界面专用样式 - 强制防夜间模式
 * Admin Page Styles - Force Light Mode
 */

/* 管理页面防夜间模式设置 */
:root {
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

html, body {
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}





/* 视频缩略图 */
.video-thumbnail {
    width: 80px;
    height: 45px;
    object-fit: cover;
    border-radius: 4px;
}

/* 缩略图优化样式 */
.thumbnail-optimized {
    transition: opacity 0.3s ease-in-out;
    opacity: 0.7;
    background-color: #f8f9fa;
}

.thumbnail-optimized.loaded {
    opacity: 1;
    background-image: none;
}

/* 视频标题 */
.video-title {
    font-weight: 600;
    color: #2c3e50;
}

/* 视频描述 */
.video-description {
    color: #6c757d;
    font-size: 0.875rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 状态徽章 */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* 操作按钮 */
.action-btn {
    padding: 0.25rem 0.5rem;
    margin: 0 1px;
}

/* 搜索区域 */
.search-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

/* 表格容器 */
.table-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}



/* 禁用的视频行 */
.video-row.disabled {
    opacity: 0.6;
    background-color: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-section {
        padding: 1rem;
    }
    
    .video-thumbnail {
        width: 60px;
        height: 34px;
    }
    
    .action-btn {
        padding: 0.2rem 0.4rem;
        margin: 0;
    }

}

/* 打印样式 */
@media print {
    .search-section,
    .action-btn,
    .loading-overlay {
        display: none !important;
    }
    
    .table-container {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* ========== 管理页面最终防夜间模式保护 ========== */
@media screen {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}

@media screen and (max-device-width: 768px) {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}