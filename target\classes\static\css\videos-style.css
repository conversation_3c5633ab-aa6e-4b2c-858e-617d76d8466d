/**
 * 视频列表页面专用样式 - 强制防夜间模式
 * Videos List Page Styles - Force Light Mode
 */

/* 视频页面防夜间模式设置 */
:root {
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

/* 页脚置底样式 */
html, body {
    height: 100%;
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

main.container {
    flex: 1;
    margin-bottom: auto;
}

footer {
    margin-top: auto;
    flex-shrink: 0;
}

/* 页脚样式 */
.videos-footer-info {
    margin: 2px !important;
}

/* 搜索框样式 */
.navbar .search-input {
    width: 250px;
}

/* 返回顶部按钮 */
.back-to-top {
    bottom: 20px;
    right: 20px;
    display: none;
    z-index: 1000;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.back-to-top:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

/* 页面头部 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 20px 20px;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 筛选区域 */
.filter-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.filter-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
}

/* 视频网格 */
.videos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* 视频卡片 */
.video-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: none;
    position: relative;
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

/* 置顶视频样式 */
.pinned-video {
    border: 2px solid #ff6b6b !important;
    box-shadow: 0 4px 20px rgba(255, 107, 107, 0.3) !important;
}

.pinned-video:hover {
    box-shadow: 0 8px 35px rgba(255, 107, 107, 0.4) !important;
}

/* 置顶标识 */
.pinned-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(135deg, #ff6b6b, #ff5252);
    color: white;
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 4px;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
    animation: pinBounce 2s ease-in-out infinite;
}

.pinned-badge i {
    font-size: 10px;
    transform: rotate(45deg);
}

/* 置顶标识动画 */
@keyframes pinBounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-2px);
    }
}

/* 视频缩略图容器 */
.video-thumbnail-container {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.video-card .card-img-top {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.video-card:hover .card-img-top {
    transform: scale(1.05);
}

/* 播放按钮覆盖层 */
.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-card:hover .play-overlay {
    opacity: 1;
}

.play-button {
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #667eea;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.play-button:hover {
    background: white;
    transform: scale(1.1);
}



/* 卡片内容 */
.video-card .card-body {
    padding: 1.25rem;
}

.video-card .card-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.4;
}

.video-card .card-text {
    color: #6c757d;
    font-size: 0.9rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.5;
    margin-bottom: 1rem;
}

/* 视频元数据 */
.video-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
    font-size: 0.85rem;
    color: #6c757d;
}

.video-format {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.video-date {
    display: flex;
    align-items: center;
}

.video-date i {
    margin-right: 0.25rem;
}

/* 页脚列布局调整 */
.col-md-6.text-md-end {
    transform: translateY(-2px);
}



/* 分页样式 */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 3rem;
}

.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: #667eea;
    font-weight: 500;
}

/* page-item active下的page-link字体设置为白色 */
.pagination .page-item.active .page-link {
    background: #667eea;
    border-color: #667eea;
    color: rgb(255, 255, 255);
}

/* page-item disabled状态样式 - 背景设置为白色 */
.pagination .page-item.disabled .page-link {
    background: #ffffff !important;
    border-color: #dee2e6 !important;
    color: #667eea !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
}

/* disabled状态的span元素样式（与disabled的a元素保持一致） */
.pagination .page-item.disabled span.page-link {
    background: #ffffff !important;
    border-color: #dee2e6 !important;
    color: #667eea !important;
    cursor: not-allowed !important;
}

/* .pagination .page-link:hover {
    background: #f8f9fa;
    color: #495057;
} */

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    color: #dee2e6;
}

.empty-state h3 {
    color: #495057;
    margin-bottom: 1rem;
}

.empty-state p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

/* 加载状态 */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
}

/* 搜索结果提示 */
.search-results-info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    color: #1976d2;
}

.search-results-info i {
    margin-right: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .navbar .search-input {
        width: 200px;
    }
    
    .page-header {
        padding: 2rem 0;
        text-align: center;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .videos-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1rem;
    }
    
    .video-thumbnail-container {
        height: 160px;
    }
    
    .filter-section {
        padding: 1rem;
    }
    
    .back-to-top {
        bottom: 15px;
        right: 15px;
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 576px) {
    .navbar .search-input {
        width: 150px;
    }
    
    .videos-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .video-card .card-body {
        padding: 1rem;
    }
    
    .page-header {
        padding: 1.5rem 0;
    }
    
    .page-header h1 {
        font-size: 1.75rem;
    }
}

/* 缩略图优化样式 */
.thumbnail-optimized {
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    opacity: 0.7;
    background-color: #f8f9fa;
    background-image:
        linear-gradient(45deg, #e9ecef 25%, transparent 25%),
        linear-gradient(-45deg, #e9ecef 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #e9ecef 75%),
        linear-gradient(-45deg, transparent 75%, #e9ecef 75%);
    background-size: 16px 16px;
    background-position: 0 0, 0 8px, 8px -8px, -8px 0px;
}

.thumbnail-optimized.loaded {
    opacity: 1;
    background-image: none;
}

.video-thumbnail:hover .thumbnail-optimized {
    transform: scale(1.02);
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .filter-section {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .filter-title {
        color: #f7fafc;
    }
    
    .video-card {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .video-card .card-title {
        color: #f7fafc;
    }
    
    .video-card .card-text {
        color: #a0aec0;
    }
    
    .video-meta {
        border-top-color: #4a5568;
        color: #a0aec0;
    }
    
    .search-results-info {
        background: #2d3748;
        border-color: #4a5568;
        color: #90cdf4;
    }
    
    .empty-state {
        color: #a0aec0;
    }
    
    .empty-state h3 {
        color: #e2e8f0;
    }
}

/* ========== 视频页面最终防夜间模式保护 ========== */
@media screen {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}

@media screen and (max-device-width: 768px) {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}
